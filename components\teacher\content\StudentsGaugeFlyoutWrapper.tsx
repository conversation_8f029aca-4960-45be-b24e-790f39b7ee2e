// Server component that fetches student data and passes it to the client component
import { use } from 'blade/server/hooks';
import { StudentsGaugeFlyout } from './StudentsGaugeFlyout.client';
import type { Student } from '../types';

interface StudentsGaugeFlyoutWrapperProps {
  setFlyout: (flyout: string | null) => void;
  teacherSlug: string;
}

export const StudentsGaugeFlyoutWrapper = ({ setFlyout, teacherSlug }: StudentsGaugeFlyoutWrapperProps) => {
  // Fetch real student data using the server-side use hook
  const users = use.users({
    where: {
      role: 'student',
      teacherId: teacherSlug,
      isActive: true
    }
  }) || [];

  // Transform the data to match our Student interface
  const students: Student[] = users.map((user: any) => ({
    id: user.id,
    name: user.name,
    email: user.email,
    classes: user.classes ? JSON.parse(user.classes) : [],
    status: user.isActive ? 'Active' : 'Inactive'
  }));

  // Pass the fetched data to the client component
  return <StudentsGaugeFlyout setFlyout={setFlyout} students={students} />;
};
