'use client';
import { useState } from 'react';
import { Dialog } from '@base-ui-components/react/dialog';
import { UserPlus, X } from 'lucide-react';
import { useMutation } from 'blade/client/hooks';
import type { StudentData } from '../types';

interface AddStudentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddStudent: (student: StudentData) => void;
}

export const AddStudentDialog = ({ 
  isOpen, 
  onClose, 
  onAddStudent 
}: AddStudentDialogProps) => {
  const [studentName, setStudentName] = useState('');
  const [studentEmail, setStudentEmail] = useState('');
  const [selectedClasses, setSelectedClasses] = useState<string[]>([]);
  const [shareWithOthers, setShareWithOthers] = useState(false);
  const [loading, setLoading] = useState(false);

  // Mock classes - in real app, this would come from the teacher's classes
  const availableClasses = [
    'Math 101',
    'Science 201', 
    'History 301',
    'English 401',
    'Art 501'
  ];

  // Use Blade's mutation hook for creating students
  const { mutate: createStudent } = useMutation({
    async mutationFn(studentData: StudentData) {
      // Generate username from name
      const username = studentData.name.toLowerCase().replace(/\s+/g, '.');

      // Generate email if not provided
      const email = studentData.email.trim() ||
        `${username}@student.school.com`;

      // Use Blade's add mutation to create the student user
      // This will trigger our user.ts trigger for proper student setup
      return await add.user.with({
        name: studentData.name,
        email,
        username,
        role: 'student',
        classes: JSON.stringify(studentData.classes), // Store as JSON string
        shareWithOtherTeachers: studentData.shareWithOtherTeachers,
        // The trigger will handle setting other defaults like slug, isActive, etc.
      });
    },
    onSuccess: (data) => {
      console.log('Student created successfully:', data);
      onAddStudent({
        name: studentName,
        email: studentEmail || `${studentName.toLowerCase().replace(/\s+/g, '.')}@student.school.com`,
        classes: selectedClasses,
        shareWithOtherTeachers: shareWithOthers
      });

      // Reset form
      setStudentName('');
      setStudentEmail('');
      setSelectedClasses([]);
      setShareWithOthers(false);
      onClose();
    },
    onError: (error) => {
      console.error('Error creating student:', error);
      // You could add toast notification here
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!studentName.trim() || selectedClasses.length === 0) return;

    setLoading(true);
    try {
      const studentData: StudentData = {
        name: studentName.trim(),
        email: studentEmail.trim(),
        classes: selectedClasses,
        shareWithOtherTeachers: shareWithOthers
      };

      await createStudent(studentData);
    } catch (error) {
      console.error('Error adding student:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleClass = (className: string) => {
    setSelectedClasses(prev => 
      prev.includes(className) 
        ? prev.filter(c => c !== className)
        : [...prev, className]
    );
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Backdrop className="fixed inset-0 bg-black opacity-20 transition-all duration-150 data-[ending-style]:opacity-0 data-[starting-style]:opacity-0 dark:opacity-70" />
        <Dialog.Popup className="fixed top-1/2 left-1/2 -mt-8 w-[500px] max-w-[calc(100vw-3rem)] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-gray-50 dark:bg-gray-900 p-6 text-gray-900 dark:text-gray-100 outline-1 outline-gray-200 dark:outline-gray-700 transition-all duration-150 data-[ending-style]:scale-90 data-[ending-style]:opacity-0 data-[starting-style]:scale-90 data-[starting-style]:opacity-0 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
                <UserPlus className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <Dialog.Title className="text-lg font-medium">
                  Add New Student
                </Dialog.Title>
                <Dialog.Description className="text-sm text-gray-600 dark:text-gray-400">
                  Create student accounts and assign them to your classes.
                </Dialog.Description>
              </div>
            </div>
            <Dialog.Close className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
              <X className="w-4 h-4" />
            </Dialog.Close>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Student Name *
              </label>
              <input
                type="text"
                value={studentName}
                onChange={(e) => setStudentName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter student's full name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Email (optional)
              </label>
              <input
                type="email"
                value={studentEmail}
                onChange={(e) => setStudentEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Will be auto-generated if not provided"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Assign to Classes *
              </label>
              <div className="grid grid-cols-2 gap-2">
                {availableClasses.map((className) => (
                  <label key={className} className="flex items-center space-x-2 p-2 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedClasses.includes(className)}
                      onChange={() => toggleClass(className)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm">{className}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2 p-3 rounded-md bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
              <input
                type="checkbox"
                id="shareWithOthers"
                checked={shareWithOthers}
                onChange={(e) => setShareWithOthers(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="shareWithOthers" className="text-sm text-blue-800 dark:text-blue-200">
                Allow other teachers from the same school to access this student's information
              </label>
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Dialog.Close className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors">
                Cancel
              </Dialog.Close>
              <button
                type="submit"
                disabled={loading || !studentName.trim() || selectedClasses.length === 0}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <UserPlus className="w-4 h-4" />
                    Add Student
                  </>
                )}
              </button>
            </div>
          </form>
        </Dialog.Popup>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
