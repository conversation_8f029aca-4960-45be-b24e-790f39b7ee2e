'use client';
import { useState } from 'react';
import { useParams } from 'blade/hooks';
import { UserPlus } from 'lucide-react';
import { Link } from 'blade/client/components';
import { AddStudentDialog } from '../dialogs/AddStudentDialog.client';
import type { StudentData, Student } from '../types';

interface StudentsGaugeFlyoutProps {
  setFlyout: (flyout: string | null) => void;
  students?: Student[];
}

// FlyoutTrigger component (assuming it exists in the parent)
const FlyoutTrigger = ({
  className,
  setFlyout
}: {
  className?: string;
  setFlyout: (flyout: string | null) => void;
}) => {
  return (
    <button
      onClick={() => setFlyout(null)}
      className={`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-7 w-7 ${className}`}
    >
      ×
    </button>
  );
};

export const StudentsGaugeFlyout = ({ setFlyout, students = [] }: StudentsGaugeFlyoutProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const params = useParams();

  // Use the students data passed as props (minimal data for flyout)
  const transformedStudents: Student[] = students;

  // Simple capacity calculation for the flyout
  const maxCapacity = 30; // Could be configurable per teacher

  const getStudentCountPercentage = () => {
    const totalStudents = transformedStudents.filter((s: Student) => s.status === 'Active').length;
    return (totalStudents / maxCapacity) * 100;
  };

  const handleAddStudent = async (studentData: StudentData) => {
    // The dialog handles the actual creation via Blade mutation
    // This callback is just for UI updates if needed
    console.log('Student added:', studentData);

    // The use hook will automatically update when the database changes
    // No need to manually update state
  };

  return (
    <>
      <div className="flex py-2 flex-col h-full">
        <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
          <div className="flex items-center justify-between mb-3">
            <button
              onClick={() => setIsDialogOpen(true)}
              className="inline-flex items-center gap-2 text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              <UserPlus className="w-5 h-5" />
              Students
            </button>
            <FlyoutTrigger className="" setFlyout={setFlyout} />
          </div>
          <p className="text-sm text-black/60 dark:text-white/60 mb-4">
            Quick overview of your students
          </p>

          <Link href={`/teacher/${params['slug']}/students`}>
            <a className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 w-full mb-4">
              Go to Students Page
            </a>
          </Link>
        </div>

        <div className="flex-1 p-4 overflow-auto">
          <div className="space-y-4">
            {/* Student Count Summary */}
            <div className="p-3 rounded-md bg-sidebar-accent/50">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Total Students</span>
                <span className="text-lg font-bold">{transformedStudents.filter((s: Student) => s.status === 'Active').length}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${getStudentCountPercentage()}%` } as React.CSSProperties}
                ></div>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                {Math.round(getStudentCountPercentage())}% of capacity
              </p>
            </div>

            {/* Quick Actions */}
            <div className="space-y-2">
              <button
                onClick={() => setIsDialogOpen(true)}
                className="w-full p-3 rounded-md bg-sidebar-accent/30 hover:bg-sidebar-accent/50 cursor-pointer text-left"
              >
                <div className="flex items-center gap-2">
                  <UserPlus className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">Add New Student</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Create student accounts and send invitations
                </p>
              </button>
            </div>

            {/* Recent Students Preview */}
            {transformedStudents.length > 0 && (
              <div className="space-y-2">
                <p className="text-xs font-medium text-gray-600">Recent Students:</p>
                {transformedStudents.slice(0, 3).map((student: Student) => (
                  <div key={student.id} className="p-2 rounded-md bg-sidebar-accent/20">
                    <p className="text-xs font-medium">{student.name}</p>
                    <p className="text-xs text-gray-500">{student.classes.length} classes</p>
                  </div>
                ))}
                {transformedStudents.length > 3 && (
                  <p className="text-xs text-gray-500 text-center py-1">
                    +{transformedStudents.length - 3} more
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <AddStudentDialog 
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onAddStudent={handleAddStudent}
      />
    </>
  );
};
