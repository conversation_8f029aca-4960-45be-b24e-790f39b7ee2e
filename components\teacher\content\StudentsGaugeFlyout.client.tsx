'use client';
import { useState } from 'react';
import { useParams } from 'blade/hooks';
import { use } from 'blade/client/hooks';
import { UserPlus } from 'lucide-react';
import { Link } from 'blade/client/components';
import { AddStudentDialog } from '../dialogs/AddStudentDialog.client';
import type { StudentData, Student, ClassItem } from '../types';

interface StudentsGaugeFlyoutProps {
  setFlyout: (flyout: string | null) => void;
}

// FlyoutTrigger component (assuming it exists in the parent)
const FlyoutTrigger = ({
  className,
  setFlyout
}: {
  className?: string;
  setFlyout: (flyout: string | null) => void;
}) => {
  return (
    <button
      onClick={() => setFlyout(null)}
      className={`inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-7 w-7 ${className}`}
    >
      ×
    </button>
  );
};

export const StudentsGaugeFlyout = ({ setFlyout }: StudentsGaugeFlyoutProps) => {
  const [activeTab, setActiveTab] = useState<'students' | 'classes'>('students');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const params = useParams();

  // Get real data from the database using Blade's use hook
  // Get students where role = 'student' and teacherId matches current teacher
  const students = use.users({
    where: {
      role: 'student',
      teacherId: params['slug'], // Access slug property correctly
      isActive: true
    }
  }) || [];

  // Transform the data to match our Student interface
  const transformedStudents: Student[] = students.map((user: any) => ({
    id: user.id,
    name: user.name,
    email: user.email,
    classes: user.classes ? JSON.parse(user.classes) : [],
    status: user.isActive ? 'Active' : 'Inactive'
  }));

  // For classes, we'll derive them from the students' class assignments
  // In a real app, you might have a separate classes table
  const classesMap = new Map<string, { studentCount: number }>();
  transformedStudents.forEach(student => {
    student.classes.forEach(className => {
      const current = classesMap.get(className) || { studentCount: 0 };
      classesMap.set(className, { studentCount: current.studentCount + 1 });
    });
  });

  const classes: ClassItem[] = Array.from(classesMap.entries()).map(([name, data]) => ({
    name,
    studentCount: data.studentCount,
    maxCapacity: 30 // Default max capacity - could be configurable
  }));

  const getStudentCountPercentage = () => {
    const totalStudents = transformedStudents.filter((s: Student) => s.status === 'Active').length;
    const maxCapacity = 30; // Could be configurable per teacher
    return (totalStudents / maxCapacity) * 100;
  };

  const handleAddStudent = async (studentData: StudentData) => {
    // The dialog handles the actual creation via Blade mutation
    // This callback is just for UI updates if needed
    console.log('Student added:', studentData);

    // The use hook will automatically update when the database changes
    // No need to manually update state
  };

  return (
    <>
      <div className="flex py-2 flex-col h-full">
        <div className="p-2 px-4 border-b border-black/20 dark:border-white/20">
          <div className="flex items-center justify-between mb-3">
            <button
              onClick={() => setIsDialogOpen(true)}
              className="inline-flex items-center gap-2 text-xl text-left font-manrope_1 font-semibold text-black/80 dark:text-white/80 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              <UserPlus className="w-5 h-5" />
              Students
            </button>
            <FlyoutTrigger className="" setFlyout={setFlyout} />
          </div>
          <p className="text-sm text-black/60 dark:text-white/60 mb-4">
            Manage your students and view analytics
          </p>

          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg mb-4">
            <button
              onClick={() => setActiveTab('students')}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'students'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Students ({transformedStudents.filter((s: Student) => s.status === 'Active').length})
            </button>
            <button
              onClick={() => setActiveTab('classes')}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'classes'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Classes ({classes.length})
            </button>
          </div>

          <Link href={`/teacher/${params['slug']}/students`}>
            <a className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 w-full mb-4">
              Go to Students Page
            </a>
          </Link>
        </div>

        <div className="flex-1 p-4 overflow-auto">
          {activeTab === 'students' ? (
            <div className="space-y-4">
              <div className="p-3 rounded-md bg-sidebar-accent/50">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Current Students</span>
                  <span className="text-lg font-bold">{transformedStudents.filter((s: Student) => s.status === 'Active').length}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Max Capacity</span>
                  <span className="text-lg font-bold">30</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${getStudentCountPercentage()}%` } as React.CSSProperties}
                  ></div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  {Math.round(getStudentCountPercentage())}% capacity used
                </p>
              </div>

              {/* Student List */}
              <div className="space-y-2">
                {transformedStudents.slice(0, 5).map((student: Student) => (
                  <div key={student.id} className="p-3 rounded-md bg-sidebar-accent/30 hover:bg-sidebar-accent/50 cursor-pointer">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{student.name}</p>
                        <p className="text-xs text-gray-600">{student.email}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          Classes: {student.classes.join(', ')}
                        </p>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        student.status === 'Active' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {student.status}
                      </span>
                    </div>
                  </div>
                ))}
                {transformedStudents.length > 5 && (
                  <p className="text-xs text-gray-500 text-center py-2">
                    +{transformedStudents.length - 5} more students
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Classes Tab Content */}
              {classes.map((classItem) => (
                <div key={classItem.name} className="p-3 rounded-md bg-sidebar-accent/50">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">{classItem.name}</span>
                    <span className="text-sm text-gray-600">
                      {classItem.studentCount}/{classItem.maxCapacity}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${(classItem.studentCount / classItem.maxCapacity) * 100}%` } as React.CSSProperties}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-600 mt-2">
                    {Math.round((classItem.studentCount / classItem.maxCapacity) * 100)}% full
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <AddStudentDialog 
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onAddStudent={handleAddStudent}
      />
    </>
  );
};
