// triggers/user.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, GetTrigger } from 'blade/types';

// Trigger for creating users
export const add: AddTrigger = (query) => {
  // Ensure query.with exists
  if (!query.with) {
    return query;
  }

  // Handle both single object and array cases
  const processUserData = (userData: any) => {
    // Set default name if not provided (use email prefix)
    if (!userData.name && userData.email) {
      userData.name = userData.email.split('@')[0];
    } else if (!userData.name) {
      userData.name = 'User';
    }

    // Special handling for students
    if (userData.role === 'student') {
      // Generate username from name if not provided
      if (!userData.username && userData.name) {
        userData.username = userData.name.toLowerCase().replace(/\s+/g, '.');
      }

      // Generate email if not provided
      if (!userData.email && userData.name) {
        userData.email = `${userData.username}@student.school.com`;
      }

      // Use username as slug for students
      if (!userData.slug && userData.username) {
        userData.slug = userData.username;
      }

      // Set default values for students
      userData.isActive = userData.isActive ?? true;
      userData.emailVerified = false; // Students don't need email verification initially
    } else {
      // Auto-generate slug if not provided - prefer email address for non-students
      if (!userData.slug) {
        let baseName = 'user';

        if (userData.email) {
          // Use full email address as base for slug (more unique and recognizable)
          baseName = userData.email.replace('@', '-at-');
        } else if (userData.name) {
          baseName = userData.name;
        }

        userData.slug = baseName
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '') || 'user';
      }
    }

    // Set default emailVerified if not provided (and not already set for students)
    if (userData.emailVerified === undefined && userData.role !== 'student') {
      userData.emailVerified = false;
    }

    // Set default role if not provided
    if (!userData.role) {
      // For OTP users (those with email), default to teacher
      // Students are created by teachers, so OTP users are always teachers or school_admins
      userData.role = userData.email ? 'teacher' : 'student';
    }

    console.log('User trigger - processed user data:', userData);

    // Set default timestamps
    userData.createdAt = new Date();
    userData.updatedAt = new Date();

    return userData;
  };

  // Handle array of users
  if (Array.isArray(query.with)) {
    query.with = query.with.map(processUserData);
  } else {
    // Handle single user
    query.with = processUserData(query.with);
  }

  return query;
};

// Trigger for updating users
export const set: SetTrigger = (query) => {
  // Ensure query.to exists
  if (!query.to) {
    return query;
  }

  // Update timestamp
  (query.to as any)['updatedAt'] = new Date();

  // Update slug if name is being changed
  if ((query.to as any)['name'] && !(query.to as any)['slug']) {
    (query.to as any)['slug'] = (query.to as any)['name']
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') || 'user';
  }

  return query;
};

// Trigger for getting users (can be used for access control)
export const get: GetTrigger = (query) => {
  // Add any access control logic here if needed
  return query;
};

// Trigger to run after user creation (synchronous)
export const afterAdd = (query: any, multiple: any, options: any) => {
  // Log student creation for debugging
  if (query.with?.role === 'student') {
    console.log('Student created successfully:', {
      name: query.with.name,
      email: query.with.email,
      username: query.with.username,
      slug: query.with.slug
    });

    // Here you could return additional queries to run after student creation:
    // 1. Create default student profile records
    // 2. Create class enrollment records
    // 3. Create notification records for teachers

    // For now, we don't need additional queries, so return empty array
  }

  // Return empty array since we don't need additional queries for now
  return [];
};