// pages/teacher/[slug]/students.tsx
import { use } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import { StudentsPageClient } from '../../../components/teacher/pages/StudentsPageClient.client';
import type { Student } from '../../../components/teacher/types';

const TeachersStudentsPage = () => {
  const params = useParams();

  // Fetch real student data using the server-side use hook
  // Make sure we only get actual students, not teachers marked as students
  const users = use.users({
    where: {
      role: 'student',
      teacherId: params['slug'],
      isActive: true
    }
  }) || [];

  // Transform the data to match our Student interface
  const students: Student[] = users.map((user: any) => ({
    id: user.id,
    name: user.name,
    email: user.email,
    classes: user.classes ? (typeof user.classes === 'string' ? JSON.parse(user.classes) : user.classes) : [],
    status: user.isActive ? 'Active' : 'Inactive'
  }));

  // Debug: Show what students we found
  console.log(`Found ${students.length} students for teacher ${params['slug']}:`,
    students.map(s => ({ name: s.name, email: s.email, id: s.id })));

  return (
    <div className="p-6">
      <StudentsPageClient students={students} />
    </div>
  );
};



export default TeachersStudentsPage;
