// pages/teacher/[slug]/students.tsx
import { use } from 'blade/server/hooks';
import { useParams } from 'blade/hooks';
import { StudentsPageClient } from '../../../components/teacher/pages/StudentsPageClient.client';
import type { Student } from '../../../components/teacher/types';

const TeachersStudentsPage = () => {
  const params = useParams();

  // Fetch real student data using the server-side use hook
  // Make sure we only get actual students, not teachers marked as students
  const users = use.users({
    where: {
      role: 'student',
      teacherId: params['slug'],
      isActive: true
    }
  }) || [];

  // Debug logging to see what we're getting
  console.log('Students query results:', users);
  console.log('Teacher slug:', params['slug']);

  // Transform the data to match our Student interface
  // Filter out any users that might be teachers incorrectly marked as students
  const students: Student[] = users
    .filter((user: any) => {
      // Additional safety check: exclude users whose email matches common teacher patterns
      // or whose slug matches the current teacher slug
      const isLikelyTeacher = user.slug === params['slug'] ||
                             user.email?.includes('coach') ||
                             user.email?.includes('teacher');

      if (isLikelyTeacher) {
        console.warn('Filtering out likely teacher account marked as student:', user);
        return false;
      }
      return true;
    })
    .map((user: any) => ({
      id: user.id,
      name: user.name,
      email: user.email,
      classes: user.classes ? JSON.parse(user.classes) : [],
      status: user.isActive ? 'Active' : 'Inactive'
    }));

  return (
    <div className="p-6">
      <StudentsPageClient students={students} />
    </div>
  );
};



export default TeachersStudentsPage;
